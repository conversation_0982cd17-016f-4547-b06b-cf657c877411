import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DashboardController extends GetxController {
  // Search controller
  final searchController = TextEditingController();

  // Observable variables
  final searchQuery = ''.obs;
  final selectedNavIndex = 0.obs;

  // Dashboard data
  final dashboardData = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load dashboard data
  void loadDashboardData() {
    dashboardData.value = {
      'totalOrders': 45,
      'onBoarding': 5,
      'designing': 5,
      'sampling': 6,
      'designPlateApproved': 5,
      'cylinderDevelopment': 5,
      'polyesterSampleApproved': 5,
      'polyesterPrinting': 1,
      'lamination': 1,
      'metallisedPasting': 1,
      'heating': 1,
      'curing': 1,
      'zipperAddition': 1,
      'slitting': 1,
      'pouching': 1,
      'sorting': 1,
      'packing': 1,
      'readyToDispatch': 1,
      'dispatched': 1,
    };
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard - already here
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    // Implement search logic here
    // Note: Removed snackbar for testing compatibility
  }

  // Handle card tap
  void onCardTap(String cardType, int count) {
    // Navigate to dashboard card detail screen
    Get.toNamed('/dashboard-card-detail', arguments: {
      'title': cardType,
      'count': count,
    });
  }




}
