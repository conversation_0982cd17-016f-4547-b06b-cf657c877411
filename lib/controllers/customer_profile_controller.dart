import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/customer_model.dart';
import '../models/customer_profile_model.dart';
import '../models/order_model.dart';

class CustomerProfileController extends GetxController {
  // Observable variables
  final RxInt selectedNavIndex = 1.obs; // Customer Profile is accessed from Customers List (index 1)
  final RxBool isLoading = false.obs;
  final Rx<CustomerProfileModel?> customerProfile = Rx<CustomerProfileModel?>(null);
  final RxList<OrderModel> customerOrders = <OrderModel>[].obs;
  final RxList<OrderProgressStep> orderProgressSteps = <OrderProgressStep>[].obs;
  final RxString notes = ''.obs;
  final RxBool isEditingNotes = false.obs;

  // Controllers
  final TextEditingController notesController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    final arguments = Get.arguments;
    if (arguments != null && arguments['customer'] != null) {
      final CustomerModel customer = arguments['customer'];
      loadCustomerProfile(customer);
    }
  }

  @override
  void onClose() {
    notesController.dispose();
    super.onClose();
  }

  // Load customer profile data
  void loadCustomerProfile(CustomerModel customer) {
    isLoading.value = true;

    // Create enhanced customer profile with sample data
    final profile = CustomerProfileModel(
      name: customer.name,
      phoneNumber: customer.phoneNumber,
      email: '<EMAIL>', // Sample email
      location: customer.location,
      businessName: 'Techo Lab',
      gstNumber: 'GST123456789',
      businessAddress: '123 Business Street, ${customer.location}',
      totalOrders: customer.orderCount,
      totalSpent: 125450.0,
      avgOrderValue: 8363.0,
      currentStatus: 'Packing',
      notes: 'Requested urgent delivery for next order. Prefers eco-friendly packaging.',
      contactLog: [
        ContactLogEntry(
          type: 'call',
          timestamp: DateTime(2024, 1, 15, 10, 30),
          description: null,
        ),
        ContactLogEntry(
          type: 'email',
          timestamp: DateTime(2024, 1, 12, 14, 15),
          description: null,
        ),
        ContactLogEntry(
          type: 'message',
          timestamp: DateTime(2024, 1, 12, 14, 15),
          description: null,
        ),
      ],
      lastOrderDate: DateTime(2025, 6, 12),
    );

    customerProfile.value = profile;
    notes.value = profile.notes ?? '';
    notesController.text = notes.value;

    // Load customer orders (sample data based on customer name)
    loadCustomerOrders(customer.name);

    // Load order progress for most recent order
    loadOrderProgress();

    isLoading.value = false;
  }

  // Load customer orders
  void loadCustomerOrders(String customerName) {
    // Sample orders for the customer
    final orders = [
      OrderModel(
        orderId: 'ORD-2024-1007',
        customerName: customerName,
        customerPhone: customerProfile.value?.phoneNumber ?? '',
        orderDate: DateTime(2025, 6, 9),
        deliveryDate: DateTime(2025, 6, 13),
        itemCount: 20,
        amount: 5000,
        status: 'Packing',
      ),
      OrderModel(
        orderId: 'ORD-2024-1008',
        customerName: customerName,
        customerPhone: customerProfile.value?.phoneNumber ?? '',
        orderDate: DateTime(2025, 6, 19),
        deliveryDate: DateTime(2025, 6, 24),
        itemCount: 40,
        amount: 10000,
        status: 'Design Approved',
      ),
      OrderModel(
        orderId: 'ORD-2024-1008',
        customerName: customerName,
        customerPhone: customerProfile.value?.phoneNumber ?? '',
        orderDate: DateTime(2025, 6, 19),
        deliveryDate: DateTime(2025, 6, 24),
        itemCount: 70,
        amount: 25000,
        status: 'Data Designing',
      ),
      OrderModel(
        orderId: 'ORD-2024-1010',
        customerName: customerName,
        customerPhone: customerProfile.value?.phoneNumber ?? '',
        orderDate: DateTime(2025, 6, 20),
        deliveryDate: DateTime(2025, 6, 25),
        itemCount: 90,
        amount: 35000,
        status: 'Heating',
      ),
    ];

    customerOrders.value = orders;
  }

  // Load order progress timeline
  void loadOrderProgress() {
    final steps = [
      OrderProgressStep(
        title: 'Packing',
        date: '4 June 2025',
        isCompleted: true,
        isActive: false,
      ),
      OrderProgressStep(
        title: 'Designing',
        date: '5 June 2025',
        isCompleted: true,
        isActive: false,
      ),
      OrderProgressStep(
        title: 'Sorting',
        date: '7 June 2025',
        isCompleted: true,
        isActive: false,
      ),
      OrderProgressStep(
        title: 'On-boarding',
        date: '7 June 2025',
        isCompleted: true,
        isActive: false,
      ),
      OrderProgressStep(
        title: 'Design Approved',
        date: '7 June 2025',
        isCompleted: true,
        isActive: false,
      ),
      OrderProgressStep(
        title: 'Heating',
        date: '8 June 2025',
        isCompleted: false,
        isActive: true,
      ),
    ];

    orderProgressSteps.value = steps;
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle edit notes
  void onEditNotes() {
    isEditingNotes.value = true;
  }

  // Handle save notes
  void onSaveNotes() {
    notes.value = notesController.text;
    if (customerProfile.value != null) {
      customerProfile.value = customerProfile.value!.copyWith(notes: notes.value);
    }
    isEditingNotes.value = false;
    
    Get.snackbar(
      'Success',
      'Notes updated successfully',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle cancel edit notes
  void onCancelEditNotes() {
    notesController.text = notes.value;
    isEditingNotes.value = false;
  }

  // Handle add contact log entry
  void onAddContactLog() {
    Get.snackbar(
      'Add Contact',
      'Add contact log functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Get recent orders (top 4)
  List<OrderModel> get recentOrders {
    return customerOrders.take(4).toList();
  }

  // Get status color
  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'packing':
        return const Color(0xFF2196F3);
      case 'design approved':
      case 'design plate approved':
        return const Color(0xFF4CAF50);
      case 'data designing':
        return const Color(0xFFFF9800);
      case 'heating':
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9E9E9E);
    }
  }
}
