import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/order_model.dart';
import '../models/add_order_model.dart';
import '../models/country_model.dart';
import '../services/firebase_service.dart';
import '../widgets/edit_order_dialog.dart';
import '../widgets/update_priority_dialog.dart';

class DashboardCardDetailController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 0.obs;
  final RxBool isLoading = false.obs;
  final RxString cardTitle = ''.obs;
  final RxInt cardCount = 0.obs;

  // Stream subscription
  StreamSubscription<List<AddOrderModel>>? _ordersSubscription;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  // Edit Order Dialog Controllers
  final TextEditingController editCustomerNameController = TextEditingController();
  final TextEditingController editMobileNumberController = TextEditingController();
  final TextEditingController editDeliveryDateController = TextEditingController();
  final TextEditingController editOrderStatusController = TextEditingController();
  final TextEditingController editOrderAmountController = TextEditingController();
  final TextEditingController editTrackingIdController = TextEditingController();
  final TextEditingController editStreetAddressController = TextEditingController();
  final TextEditingController editCityController = TextEditingController();
  final TextEditingController editStateController = TextEditingController();
  final TextEditingController editPinCodeController = TextEditingController();

  // Edit Order Observable Variables
  final Rx<CountryModel> editSelectedCountry = CountryModel(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  ).obs;

  @override
  void onInit() {
    super.onInit();

    // Get parameters from route
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      cardTitle.value = arguments['title'] ?? '';
      cardCount.value = arguments['count'] ?? 0;
      loadOrdersForCard(cardTitle.value, cardCount.value);
    }

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
      onSearchChanged(searchController.text);
    });
  }

  @override
  void onClose() {
    _ordersSubscription?.cancel();
    searchController.dispose();
    super.onClose();
  }

  // Load orders based on card type from Firebase
  void loadOrdersForCard(String cardType, int count) {
    isLoading.value = true;

    try {
      // Listen to orders stream from Firebase
      _ordersSubscription = _firebaseService.getOrdersStream().listen(
        (addOrderModels) {
          // Filter orders by status and convert to OrderModel
          final filteredAddOrders = addOrderModels.where((order) {
            if (cardType == 'Total Orders') {
              return true; // Show all orders for Total Orders card
            }
            return order.orderStatus == _getStatusKey(cardType);
          }).toList();

          // Convert AddOrderModel to OrderModel for display
          final orderModels = filteredAddOrders.map((addOrder) => _convertToOrderModel(addOrder)).toList();

          orders.value = orderModels;
          filteredOrders.value = orderModels;
          isLoading.value = false;
        },
        onError: (error) {
          log('Error loading orders for card: $error');
          isLoading.value = false;
          Get.snackbar(
            'Error',
            'Failed to load orders. Please try again.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
      );
    } catch (e) {
      log('Error setting up orders stream for card: $e');
      isLoading.value = false;
      Get.snackbar(
        'Error',
        'Failed to load orders. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Convert AddOrderModel to OrderModel for display
  OrderModel _convertToOrderModel(AddOrderModel addOrder) {
    return OrderModel(
      orderId: addOrder.orderId,
      customerName: addOrder.customerName,
      customerPhone: addOrder.mobileNumber,
      orderDate: addOrder.createdAt ?? DateTime.now(),
      deliveryDate: addOrder.deliveryDate ?? DateTime.now().add(Duration(days: 10)),
      itemCount: addOrder.quantity,
      amount: addOrder.price * addOrder.quantity,
      status: addOrder.orderStatus,
    );
  }

  // Get status key for filtering (using exact Firebase status names)
  String _getStatusKey(String cardType) {
    switch (cardType) {
      case 'On-boarding':
        return 'On-boarding';
      case 'Designing':
        return 'Designing';
      case 'Sampling':
        return 'Sampling';
      case 'Design Plate Approved':
        return 'Design Plate Approved';
      case 'Cylinder Development':
        return 'Cylinder Development';
      case 'Polyester Sample Approved':
        return 'Polyester Sample Approved';
      case 'Polyester Printing':
        return 'Polyester Printing';
      case 'Lamination':
        return 'Lamination';
      case 'Metallised Pasting':
        return 'Metallised Pasting';
      case 'Heating':
        return 'Heating';
      case 'Curing':
        return 'Curing';
      case 'Zipper Addition':
        return 'Zipper Addition';
      case 'Slitting':
        return 'Slitting';
      case 'Pouching':
        return 'Pouching';
      case 'Sorting':
        return 'Sorting';
      case 'Packing':
        return 'Packing';
      case 'Ready to Dispatch':
        return 'Ready to Dispatch';
      case 'Dispatched':
        return 'Dispatched';
      default:
        return cardType; // Return as-is for exact match
    }
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredOrders.value = orders;
    } else {
      filteredOrders.value = orders.where((order) {
        return order.orderId.toLowerCase().contains(query.toLowerCase()) ||
               order.customerName.toLowerCase().contains(query.toLowerCase()) ||
               order.customerPhone.contains(query);
      }).toList();
    }
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle back to dashboard
  void onBackToDashboard() {
    Get.back();
  }

  // Handle add order
  void onAddOrderTap() {
    Get.toNamed('/add-order');
  }

  // Handle order actions
  void onViewDetails(OrderModel order) {
    Get.snackbar(
      'View Details',
      'Viewing details for ${order.orderId}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void onEditOrder(OrderModel order) {
    _showEditOrderDialog(order);
  }

  // Show Edit Order Dialog
  void _showEditOrderDialog(OrderModel order) {
    _prefillEditForm(order);
    Get.dialog(
      EditOrderDialog(
        order: order,
        onSave: (updatedOrder) => _handleOrderUpdate(updatedOrder),
      ),
      barrierDismissible: false,
    );
  }

  // Prefill edit form with existing order data
  void _prefillEditForm(OrderModel order) {
    editCustomerNameController.text = order.customerName;
    editMobileNumberController.text = order.customerPhone.replaceAll('+91', '').trim();
    editDeliveryDateController.text = _formatDateForInput(order.deliveryDate);
    editOrderStatusController.text = order.status;
    editOrderAmountController.text = order.amount.toString();
    editTrackingIdController.text = 'TRK${order.orderId.replaceAll('ORD-', '')}';

    // Generate realistic address data based on order characteristics
    final addressData = _generateAddressData(order);
    editStreetAddressController.text = addressData['street']!;
    editCityController.text = addressData['city']!;
    editStateController.text = addressData['state']!;
    editPinCodeController.text = addressData['pinCode']!;
  }

  // Generate address data based on order
  Map<String, String> _generateAddressData(OrderModel order) {
    final addresses = [
      {
        'street': '123 Business Street',
        'city': 'Mumbai',
        'state': 'Maharashtra',
        'pinCode': '400001'
      },
      {
        'street': '456 Industrial Area',
        'city': 'Delhi',
        'state': 'Delhi',
        'pinCode': '110001'
      },
      {
        'street': '789 Commercial Complex',
        'city': 'Bangalore',
        'state': 'Karnataka',
        'pinCode': '560001'
      },
      {
        'street': '321 Trade Center',
        'city': 'Chennai',
        'state': 'Tamil Nadu',
        'pinCode': '600001'
      },
      {
        'street': '654 Market Plaza',
        'city': 'Pune',
        'state': 'Maharashtra',
        'pinCode': '411001'
      },
      {
        'street': '987 Corporate Hub',
        'city': 'Hyderabad',
        'state': 'Telangana',
        'pinCode': '500001'
      },
    ];

    // Use order ID to consistently generate the same address for the same order
    final index = order.orderId.hashCode.abs() % addresses.length;
    return addresses[index];
  }

  // Handle order update
  void _handleOrderUpdate(OrderModel updatedOrder) {
    // Update the order in the local list
    final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
    if (orderIndex != -1) {
      orders[orderIndex] = updatedOrder;

      // Also update filtered orders if they contain this order
      final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (filteredIndex != -1) {
        filteredOrders[filteredIndex] = updatedOrder;
      }
    }

    Get.back(); // Close dialog
    Get.snackbar(
      'Success',
      'Order ${updatedOrder.orderId} updated successfully!',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Select country for edit dialog
  void selectEditCountry(CountryModel country) {
    editSelectedCountry.value = country;
  }

  // Format date for input field
  String _formatDateForInput(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void onUpdateStatus(OrderModel order) async {
    final result = await Get.toNamed('/update-status', arguments: {
      'order': order,
      'sourceScreen': 'dashboard',
    });

    // Handle the updated order data if status was updated
    if (result != null && result['wasUpdated'] == true) {
      final updatedOrder = result['updatedOrder'] as OrderModel;

      // Update the order in the local list
      final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (orderIndex != -1) {
        orders[orderIndex] = updatedOrder;

        // Also update filtered orders if they contain this order
        final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
        if (filteredIndex != -1) {
          filteredOrders[filteredIndex] = updatedOrder;
        }
      }
    }
  }

  // Handle Update Priority button press
  void onUpdatePriority(OrderModel order) {
    Get.dialog(
      UpdatePriorityDialog(
        order: order,
        onSave: (updatedOrder) => _handlePriorityUpdate(updatedOrder),
      ),
      barrierDismissible: false,
    );
  }

  // Handle priority update
  void _handlePriorityUpdate(OrderModel updatedOrder) {
    // Update the order in the local list
    final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
    if (orderIndex != -1) {
      orders[orderIndex] = updatedOrder;

      // Also update filtered orders if they contain this order
      final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (filteredIndex != -1) {
        filteredOrders[filteredIndex] = updatedOrder;
      }
    }

    Get.back(); // Close dialog
    Get.snackbar(
      'Success',
      'Priority updated successfully for Order ${updatedOrder.orderId}!',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Get priority for order (updated logic)
  String getPriority(OrderModel order) {
    // Return the stored priority if available, otherwise use dummy logic
    if (order.priority != null && order.priority!.isNotEmpty) {
      return order.priority!;
    }

    // Fallback logic: orders with higher amounts get higher priority
    if (order.amount >= 7000) return 'High Priority';
    if (order.amount >= 4000) return 'Medium Priority';
    return 'Low Priority';
  }

  // Get priority color
  Color getPriorityColor(String priority) {
    switch (priority) {
      case 'High Priority':
        return const Color(0xFFFF6B6B);
      case 'Medium Priority':
        return const Color(0xFFFFB347);
      case 'Low Priority':
        return const Color(0xFF6B73FF);
      case 'No Priority':
        return const Color(0xFF9B59B6);
      default:
        return Colors.grey;
    }
  }

  // Get notes for order (updated logic)
  String getNotes(OrderModel order) {
    // Return the stored notes if available, otherwise use dummy logic
    if (order.notes != null && order.notes!.isNotEmpty) {
      return order.notes!;
    }

    // Fallback dummy notes
    List<String> notes = [
      'Urgent Delivery Required for Client',
      'Standard delivery timeline acceptable',
      'Client prefers morning delivery',
      'Handle with care - fragile items'
    ];
    return notes[order.orderId.hashCode % notes.length];
  }
}
