import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/add_order_model.dart';
import '../models/country_model.dart';
import '../services/firebase_service.dart';

class AddOrderController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;
  final ImagePicker _imagePicker = ImagePicker();

  // Observable variables
  final RxInt selectedNavIndex = 2.obs; // Add Order is accessed from Orders List (index 2)
  final RxBool isLoading = false.obs;
  final RxBool isUploadingImage = false.obs;
  final RxString selectedPayment = 'Paid'.obs;
  final RxString selectedOrderStatus = 'Packing'.obs;
  final RxString productImagePath = ''.obs;
  final RxString productImageUrl = ''.obs;
  final Rx<Uint8List?> productImageBytes = Rx<Uint8List?>(null);
  final Rx<CountryModel> selectedCountry = CountryModel(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  ).obs;

  // Form controllers
  final TextEditingController customerNameController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController gstNumberController = TextEditingController();
  final TextEditingController emailIdController = TextEditingController();
  final TextEditingController businessNameController = TextEditingController();
  final TextEditingController businessAddressController = TextEditingController();
  final TextEditingController orderIdController = TextEditingController();
  final TextEditingController productNameController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  // Dropdown options
  final List<String> paymentOptions = ['Paid', 'Pending', 'Partial'];
  final List<String> orderStatusOptions = [
    'Packing',
    'On-boarding',
    'Designing',
    'Sampling',
    'Design Plate Approved',
    'Cylinder Development',
    'Polyester Sample Approved',
    'Polyester Printing',
    'Lamination',
    'Metallised Pasting',
    'Heating',
    'Curing',
    'Zipper Addition',
    'Slitting',
    'Pouching',
    'Sorting',
    'Ready to Dispatch',
    'Dispatched'
  ];

  @override
  void onInit() {
    super.onInit();
    _generateOrderId();
    _initializeMobileNumber();
  }

  @override
  void onClose() {
    customerNameController.dispose();
    mobileNumberController.dispose();
    gstNumberController.dispose();
    emailIdController.dispose();
    businessNameController.dispose();
    businessAddressController.dispose();
    orderIdController.dispose();
    productNameController.dispose();
    priceController.dispose();
    quantityController.dispose();
    searchController.dispose();
    super.onClose();
  }

  // Generate unique order ID
  void _generateOrderId() {
    final now = DateTime.now();
    final orderId = 'ORD-${now.year}-${now.millisecondsSinceEpoch.toString().substring(8)}';
    orderIdController.text = orderId;
  }

  // Initialize mobile number with default country code
  void _initializeMobileNumber() {
    mobileNumberController.text = '${selectedCountry.value.dialCode} ';
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle payment selection
  void selectPayment(String payment) {
    selectedPayment.value = payment;
  }

  // Handle order status selection
  void selectOrderStatus(String status) {
    selectedOrderStatus.value = status;
  }

  // Handle country selection
  void selectCountry(CountryModel country) {
    selectedCountry.value = country;

    // Update mobile number with new country code if the field is empty or contains old country code
    final currentText = mobileNumberController.text.trim();
    if (currentText.isEmpty || currentText.startsWith('+')) {
      // If field is empty or starts with a country code, replace with new country code
      mobileNumberController.text = '${country.dialCode} ';
    }
  }

  // Handle product image upload
  void onUploadImageTap() {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Image Source',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImageSourceOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildImageSourceOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              ],
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          children: [
            Icon(icon, size: 40, color: Colors.blue),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Pick image from camera or gallery
  Future<void> _pickImage(ImageSource source) async {
    try {
      Get.back(); // Close bottom sheet

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        // For web compatibility, we need to read the file as bytes
        final Uint8List imageBytes = await pickedFile.readAsBytes();

        // Store bytes for web preview and path for mobile preview
        productImageBytes.value = imageBytes;
        print('Image selected - Web: $kIsWeb, Bytes length: ${imageBytes.length}');

        if (kIsWeb) {
          // For web, we'll use a placeholder path to indicate image is selected
          productImagePath.value = 'web_image_selected';
          print('Web: Set productImageBytes with ${imageBytes.length} bytes');
        } else {
          // For mobile, use the file path
          productImagePath.value = pickedFile.path;
          print('Mobile: Set productImagePath to ${pickedFile.path}');
        }

        // No upload here - just store for preview
        // Upload will happen when user clicks "Add Order" button
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to pick image. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Handle form submission
  void onAddOrderTap() async {
    if (_validateForm()) {
      isLoading.value = true;

      try {
        String? imageUrl;

        // Upload image first if one is selected
        if (productImageBytes.value != null) {
          imageUrl = await _firebaseService.uploadProductImage(
            productImageBytes.value!,
            orderIdController.text.trim(),
            'product_image.jpg',
          );

          if (imageUrl != null) {
            productImageUrl.value = imageUrl;
          }
        }

        final orderData = AddOrderModel(
          customerName: customerNameController.text.trim(),
          mobileNumber: mobileNumberController.text.trim(),
          gstNumber: gstNumberController.text.trim(),
          emailId: emailIdController.text.trim(),
          businessName: businessNameController.text.trim(),
          businessAddress: businessAddressController.text.trim(),
          orderId: orderIdController.text.trim(),
          productName: productNameController.text.trim(),
          price: double.tryParse(priceController.text) ?? 0.0,
          quantity: int.tryParse(quantityController.text) ?? 0,
          payment: selectedPayment.value,
          orderStatus: selectedOrderStatus.value,
          productImagePath: productImagePath.value.isEmpty ? null : productImagePath.value,
          productImageUrl: imageUrl ?? (productImageUrl.value.isEmpty ? null : productImageUrl.value),
        );

        // Save order data to Firestore
        final success = await _firebaseService.saveOrder(orderData);

        if (success) {
          Get.snackbar(
            'Success',
            'Order ${orderData.orderId} added successfully!',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          // Clear form and navigate back
          _clearForm();
          Get.offNamed('/order-list');
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to save order. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }

  // Clear form data
  void _clearForm() {
    customerNameController.clear();
    mobileNumberController.clear();
    gstNumberController.clear();
    emailIdController.clear();
    businessNameController.clear();
    businessAddressController.clear();
    productNameController.clear();
    priceController.clear();
    quantityController.clear();
    productImagePath.value = '';
    productImageUrl.value = '';
    productImageBytes.value = null;
    selectedPayment.value = 'Paid';
    selectedOrderStatus.value = 'Packing';
    _generateOrderId();
    _initializeMobileNumber();
  }

  // Form validation
  bool _validateForm() {
    if (customerNameController.text.isEmpty) {
      _showError('Please enter customer name');
      return false;
    }
    if (mobileNumberController.text.isEmpty) {
      _showError('Please enter mobile number');
      return false;
    }
    if (orderIdController.text.isEmpty) {
      _showError('Please enter order ID');
      return false;
    }
    if (productNameController.text.isEmpty) {
      _showError('Please enter product name');
      return false;
    }
    if (priceController.text.isEmpty || double.tryParse(priceController.text) == null) {
      _showError('Please enter valid price');
      return false;
    }
    if (quantityController.text.isEmpty || int.tryParse(quantityController.text) == null) {
      _showError('Please enter valid quantity');
      return false;
    }
    return true;
  }

  // Show error message
  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  // Handle search
  void onSearchChanged(String query) {
    // TODO: Implement search functionality if needed
  }
}
