import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/customer_model.dart';

class CustomersListController extends GetxController {
  // Observable variables
  final RxList<CustomerModel> customers = <CustomerModel>[].obs;
  final RxList<CustomerModel> filteredCustomers = <CustomerModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 1.obs; // Customers List is index 1
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 10.obs;
  final RxBool isLoading = false.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadCustomersData();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load sample customers data
  void loadCustomersData() {
    isLoading.value = true;

    // Sample data matching the image
    final sampleCustomers = [
      CustomerModel(
        name: 'Krishna Chaitanya',
        phoneNumber: '+91 8522006009',
        location: 'New York',
        orderCount: 3,
      ),
      CustomerModel(
        name: 'Kumar Rajesh',
        phoneNumber: '+91 8877771523',
        location: 'Hyderabad',
        orderCount: 4,
      ),
      CustomerModel(
        name: 'Chaitanya',
        phoneNumber: '+91 7275528900',
        location: 'Chennai',
        orderCount: 5,
      ),
      CustomerModel(
        name: 'Ram',
        phoneNumber: '+91 6352106009',
        location: 'Coimbatore',
        orderCount: 6,
      ),
      CustomerModel(
        name: 'Mohan Priyan',
        phoneNumber: '+91 9910418601',
        location: 'Mumbai',
        orderCount: 6,
      ),
      CustomerModel(
        name: 'Prem Kumar',
        phoneNumber: '+91 9944756791',
        location: 'Chennai',
        orderCount: 7,
      ),
      CustomerModel(
        name: 'Sathish Kumar',
        phoneNumber: '+91 9944766210',
        location: 'Mysore',
        orderCount: 4,
      ),
      CustomerModel(
        name: 'sharama',
        phoneNumber: '+91 9876871541',
        location: 'Pondicherry',
        orderCount: 3,
      ),
      CustomerModel(
        name: 'Teja',
        phoneNumber: '+91 8464500645',
        location: 'Mumbai',
        orderCount: 3,
      ),
      CustomerModel(
        name: 'Praveen',
        phoneNumber: '+91 8942110987',
        location: 'Nepal',
        orderCount: 2,
      ),
      CustomerModel(
        name: 'Praveen',
        phoneNumber: '+91 7894578510',
        location: 'Delhi',
        orderCount: 7,
      ),
      // Additional customers to test pagination (more than 10 items)
      CustomerModel(
        name: 'Arjun Sharma',
        phoneNumber: '+91 9876543210',
        location: 'Bangalore',
        orderCount: 5,
      ),
      CustomerModel(
        name: 'Meera Patel',
        phoneNumber: '+91 8765432109',
        location: 'Ahmedabad',
        orderCount: 8,
      ),
      CustomerModel(
        name: 'Vikram Singh',
        phoneNumber: '+91 7654321098',
        location: 'Jaipur',
        orderCount: 3,
      ),
      CustomerModel(
        name: 'Anita Reddy',
        phoneNumber: '+91 6543210987',
        location: 'Hyderabad',
        orderCount: 9,
      ),
      CustomerModel(
        name: 'Rohit Gupta',
        phoneNumber: '+91 5432109876',
        location: 'Pune',
        orderCount: 4,
      ),
      CustomerModel(
        name: 'Priya Nair',
        phoneNumber: '+91 4321098765',
        location: 'Kochi',
        orderCount: 6,
      ),
    ];

    customers.value = sampleCustomers;
    filteredCustomers.value = sampleCustomers;
    isLoading.value = false;
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredCustomers.value = customers;
    } else {
      filteredCustomers.value = customers.where((customer) {
        return customer.name.toLowerCase().contains(query.toLowerCase()) ||
               customer.phoneNumber.contains(query) ||
               customer.location.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
    currentPage.value = 1; // Reset to first page when searching
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List - already here
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle add customer
  void onAddCustomerTap() {
    Get.toNamed('/add-customer');
  }

  // Handle customer action (view)
  void onCustomerActionTap(CustomerModel customer) {
    Get.toNamed('/customer-profile', arguments: {
      'customer': customer,
    });
  }

  // Get paginated customers
  List<CustomerModel> get paginatedCustomers {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= filteredCustomers.length) return [];

    return filteredCustomers.sublist(
      startIndex,
      endIndex > filteredCustomers.length ? filteredCustomers.length : endIndex,
    );
  }

  // Get total pages
  int get totalPages {
    return (filteredCustomers.length / itemsPerPage.value).ceil();
  }

  // Change page
  void changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      currentPage.value = page;
    }
  }
}
