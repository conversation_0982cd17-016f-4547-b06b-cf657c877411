import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:typed_data';
import '../models/add_order_model.dart';
import '../models/add_customer_model.dart';

class FirebaseService extends GetxController {
  static FirebaseService get instance => Get.find();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Observable user state
  final Rx<User?> _user = Rx<User?>(null);
  User? get user => _user.value;

  // Observable user data from Firestore
  final Rx<Map<String, dynamic>?> _userData = Rx<Map<String, dynamic>?>(null);
  Map<String, dynamic>? get userData => _userData.value;

  // Loading states
  final isLoading = false.obs;
  final isSigningIn = false.obs;
  final isSigningUp = false.obs;
  final isResettingPassword = false.obs;

  @override
  void onInit() {
    super.onInit();
    // Listen to auth state changes and handle session persistence
    _user.bindStream(_auth.authStateChanges());

    // Listen to user changes and fetch user data when user signs in
    ever(_user, (User? user) {
      if (user != null) {
        _fetchUserData(user.uid);
      } else {
        _userData.value = null;
      }
    });
  }

  /// Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      isSigningIn.value = true;

      final UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      // Create or update user data in Firestore after successful sign in
      if (userCredential.user != null) {
        await _createOrUpdateUserData(userCredential.user!);
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      _handleAuthException(e);
      return null;
    } catch (e) {
      Get.snackbar(
        'Error',
        'An unexpected error occurred. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      isSigningIn.value = false;
    }
  }

  /// Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      isSigningUp.value = true;

      final UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      // Create user data in Firestore after successful sign up
      if (userCredential.user != null) {
        await _createOrUpdateUserData(userCredential.user!);
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      _handleAuthException(e);
      return null;
    } catch (e) {
      Get.snackbar(
        'Error',
        'An unexpected error occurred. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      isSigningUp.value = false;
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail({required String email}) async {
    try {
      isResettingPassword.value = true;

      await _auth.sendPasswordResetEmail(email: email.trim());

      Get.snackbar(
        'Success',
        'Password reset email sent. Please check your inbox.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } on FirebaseAuthException catch (e) {
      _handleAuthException(e);
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'An unexpected error occurred. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isResettingPassword.value = false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      Get.snackbar(
        'Success',
        'Signed out successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to sign out. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Check if user is signed in
  bool get isSignedIn => _user.value != null;

  /// Get current user email
  String? get currentUserEmail => _user.value?.email;

  /// Get current user ID
  String? get currentUserId => _user.value?.uid;

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate password strength
  bool isValidPassword(String password) {
    // At least 6 characters
    return password.length >= 6;
  }

  /// Handle Firebase Auth exceptions
  void _handleAuthException(FirebaseAuthException e) {
    String message;

    switch (e.code) {
      case 'user-not-found':
        message = 'No user found with this email address.';
        break;
      case 'wrong-password':
        message = 'Incorrect password. Please try again.';
        break;
      case 'email-already-in-use':
        message = 'An account already exists with this email address.';
        break;
      case 'weak-password':
        message = 'Password is too weak. Please choose a stronger password.';
        break;
      case 'invalid-email':
        message = 'Please enter a valid email address.';
        break;
      case 'user-disabled':
        message = 'This account has been disabled. Please contact support.';
        break;
      case 'too-many-requests':
        message = 'Too many failed attempts. Please try again later.';
        break;
      case 'operation-not-allowed':
        message = 'Email/password sign-in is not enabled. Please contact support.';
        break;
      case 'invalid-credential':
        message = 'Invalid email or password. Please check your credentials.';
        break;
      default:
        message = 'Authentication failed: ${e.message ?? 'Unknown error'}';
    }

    Get.snackbar(
      'Authentication Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
    );
  }

  /// Update user profile
  Future<bool> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await user.updateDisplayName(displayName);
        await user.updatePhotoURL(photoURL);
        await user.reload();
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update profile. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Send email verification
  Future<bool> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        Get.snackbar(
          'Success',
          'Verification email sent. Please check your inbox.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to send verification email. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Check if current user's email is verified
  bool get isEmailVerified => _user.value?.emailVerified ?? false;

  /// Reload current user data
  Future<void> reloadUser() async {
    await _auth.currentUser?.reload();
  }

  // ==================== FIRESTORE METHODS ====================

  /// Create or update user data in Firestore
  Future<void> _createOrUpdateUserData(User user) async {
    try {
      final userDoc = _firestore.collection('users').doc(user.uid);

      // Check if user document exists
      final docSnapshot = await userDoc.get();

      final userData = {
        'uid': user.uid,
        'email': user.email,
        'emailVerified': user.emailVerified,
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'lastSignIn': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (docSnapshot.exists) {
        // Update existing user data
        await userDoc.update(userData);
      } else {
        // Create new user document
        userData['createdAt'] = FieldValue.serverTimestamp();
        await userDoc.set(userData);

        Get.snackbar(
          'Welcome!',
          'Your account has been created successfully.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('Error creating/updating user data: $e');
      // Don't show error to user as this is not critical for login
    }
  }

  /// Fetch user data from Firestore
  Future<void> _fetchUserData(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (userDoc.exists) {
        _userData.value = userDoc.data();
      } else {
        _userData.value = null;
      }
    } catch (e) {
      print('Error fetching user data: $e');
      _userData.value = null;
    }
  }

  /// Get user data from Firestore (one-time fetch)
  Future<Map<String, dynamic>?> getUserData(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      return userDoc.exists ? userDoc.data() : null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  /// Update user profile in Firestore
  Future<bool> updateUserDataInFirestore({
    String? displayName,
    String? photoURL,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (displayName != null) updateData['displayName'] = displayName;
      if (photoURL != null) updateData['photoURL'] = photoURL;
      if (additionalData != null) updateData.addAll(additionalData);

      await _firestore.collection('users').doc(user.uid).update(updateData);

      // Refresh local user data
      await _fetchUserData(user.uid);

      return true;
    } catch (e) {
      print('Error updating user data: $e');
      Get.snackbar(
        'Error',
        'Failed to update profile. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Delete user account and data
  Future<bool> deleteUserAccount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // Delete user document from Firestore
      await _firestore.collection('users').doc(user.uid).delete();

      // Delete user account from Firebase Auth
      await user.delete();

      Get.snackbar(
        'Account Deleted',
        'Your account has been deleted successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error deleting user account: $e');
      Get.snackbar(
        'Error',
        'Failed to delete account. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Listen to user data changes in real-time
  Stream<DocumentSnapshot<Map<String, dynamic>>> getUserDataStream(String uid) {
    return _firestore.collection('users').doc(uid).snapshots();
  }

  /// Check if user data exists in Firestore
  Future<bool> userDataExists(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      return userDoc.exists;
    } catch (e) {
      print('Error checking user data existence: $e');
      return false;
    }
  }

  // ==================== ORDER MANAGEMENT METHODS ====================

  /// Upload image to Firebase Storage (Web and Mobile compatible)
  Future<String?> uploadProductImage(Uint8List imageBytes, String orderId, String fileName) async {
    try {
      // Create a unique filename if not provided
      final finalFileName = fileName.isNotEmpty
          ? fileName
          : 'product_${orderId}_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // Create reference to Firebase Storage
      final storageRef = _storage.ref().child('product_images/$finalFileName');

      // Upload the file using putData for web compatibility
      final uploadTask = storageRef.putData(
        imageBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'orderId': orderId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      // Wait for upload to complete
      final snapshot = await uploadTask;

      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      print('Error uploading image: $e');
      Get.snackbar(
        'Error',
        'Failed to upload image. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  /// Save order to Firestore
  Future<bool> saveOrder(AddOrderModel order) async {
    try {
      final now = DateTime.now();
      final deliveryDate = now.add(Duration(days: 10)); // Set delivery date to 10 days from now

      final orderData = order.copyWith(
        createdAt: now,
        updatedAt: now,
        deliveryDate: deliveryDate,
      );

      await _firestore
          .collection('orders')
          .doc(order.orderId)
          .set(orderData.toJson());

      return true;
    } catch (e) {
      print('Error saving order: $e');
      Get.snackbar(
        'Error',
        'Failed to save order. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Get all orders from Firestore
  Future<List<AddOrderModel>> getOrders() async {
    try {
      final querySnapshot = await _firestore
          .collection('orders')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => AddOrderModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching orders: $e');
      return [];
    }
  }

  /// Get order by ID
  Future<AddOrderModel?> getOrderById(String orderId) async {
    try {
      final doc = await _firestore.collection('orders').doc(orderId).get();

      if (doc.exists && doc.data() != null) {
        return AddOrderModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error fetching order: $e');
      return null;
    }
  }

  /// Update order in Firestore
  Future<bool> updateOrder(AddOrderModel order) async {
    try {
      final orderData = order.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('orders')
          .doc(order.orderId)
          .update(orderData.toJson());

      return true;
    } catch (e) {
      print('Error updating order: $e');
      Get.snackbar(
        'Error',
        'Failed to update order. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Delete order from Firestore
  Future<bool> deleteOrder(String orderId) async {
    try {
      await _firestore.collection('orders').doc(orderId).delete();
      return true;
    } catch (e) {
      print('Error deleting order: $e');
      Get.snackbar(
        'Error',
        'Failed to delete order. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Listen to orders in real-time
  Stream<List<AddOrderModel>> getOrdersStream() {
    return _firestore
        .collection('orders')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => AddOrderModel.fromJson(doc.data()))
            .toList());
  }

  // ==================== CUSTOMER MANAGEMENT METHODS ====================

  /// Save customer to Firestore
  Future<bool> saveCustomer(AddCustomerModel customer) async {
    try {
      final customerData = customer.copyWith(
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .set(customerData.toJson());

      return true;
    } catch (e) {
      print('Error saving customer: $e');
      Get.snackbar(
        'Error',
        'Failed to save customer. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Get all customers from Firestore
  Future<List<AddCustomerModel>> getCustomers() async {
    try {
      final querySnapshot = await _firestore
          .collection('customers')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => AddCustomerModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching customers: $e');
      return [];
    }
  }

  /// Get customer by ID
  Future<AddCustomerModel?> getCustomerById(String customerId) async {
    try {
      final doc = await _firestore.collection('customers').doc(customerId).get();

      if (doc.exists && doc.data() != null) {
        return AddCustomerModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error fetching customer: $e');
      return null;
    }
  }

  /// Update customer in Firestore
  Future<bool> updateCustomer(AddCustomerModel customer) async {
    try {
      final customerData = customer.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .update(customerData.toJson());

      return true;
    } catch (e) {
      print('Error updating customer: $e');
      Get.snackbar(
        'Error',
        'Failed to update customer. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Delete customer from Firestore
  Future<bool> deleteCustomer(String customerId) async {
    try {
      await _firestore.collection('customers').doc(customerId).delete();
      return true;
    } catch (e) {
      print('Error deleting customer: $e');
      Get.snackbar(
        'Error',
        'Failed to delete customer. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Listen to customers in real-time
  Stream<List<AddCustomerModel>> getCustomersStream() {
    return _firestore
        .collection('customers')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => AddCustomerModel.fromJson(doc.data()))
            .toList());
  }

  /// Update customer notes in Firestore
  Future<bool> updateCustomerNotes(String customerId, String notes) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customerId)
          .update({
        'notes': notes,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      print('Error updating customer notes: $e');
      Get.snackbar(
        'Error',
        'Failed to update notes. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Get customer by phone number
  Future<AddCustomerModel?> getCustomerByPhone(String phoneNumber) async {
    try {
      final querySnapshot = await _firestore
          .collection('customers')
          .where('mobileNumber', isEqualTo: phoneNumber)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return AddCustomerModel.fromJson(querySnapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      print('Error fetching customer by phone: $e');
      return null;
    }
  }
}
