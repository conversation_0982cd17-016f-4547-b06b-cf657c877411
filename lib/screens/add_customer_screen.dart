import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/country_picker.dart';
import '../controllers/add_customer_controller.dart';

class AddCustomerScreen extends GetView<AddCustomerController> {
  const AddCustomerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: _buildMobileAppBar(),
              drawer: _buildMobileDrawer(),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              // Left Sidebar
              _buildSidebar(),

              // Main Content
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  PreferredSizeWidget _buildMobileAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder:
            (context) => IconButton(
              icon: PlatformIcon(
                iconName: 'menu',
                size: MySize.size24,
                color: AppColors.blackColor,
              ),
              onPressed: () => Scaffold.of(context).openDrawer(),
            ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SmartIcon(
            assetPath: 'assets/icons/logo_icon.svg',
            height: MySize.size80,
            width: MySize.size84,
            color: AppColors.blackColor,
          ),
        ],
      ),
      centerTitle: false,
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child: Column(
                children: [
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: AppColors.blackColor.withValues(alpha: 0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),

            // Navigation Items
            Expanded(
              child: Obx(
                () => Column(
                  children: [
                    _buildMobileNavItem(
                      icon: 'home',
                      label: 'Dashboard',
                      isSelected: controller.selectedNavIndex.value == 0,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(0);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'person',
                      label: 'Customers List',
                      isSelected: controller.selectedNavIndex.value == 1,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(1);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'shopping_cart',
                      label: 'Orders List',
                      isSelected: controller.selectedNavIndex.value == 2,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(2);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color:
                      isSelected
                          ? AppColors.primaryColor
                          : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth <= 768;

        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with Search Bar
              _buildHeader(isMobile: isMobile),

              SizedBox(height: MySize.size24),

              // Form Content
              Expanded(
                child: SingleChildScrollView(
                  child: _buildFormContent(isMobile: isMobile),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader({bool isMobile = false}) {
    return Row(
      children: [
        // Title Section
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Customer',
                style: TextStyle(
                  fontSize: isMobile ? MySize.size20 : MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                'Add customer details to link with orders and business information',
                style: TextStyle(
                  fontSize: isMobile ? MySize.size12 : MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        if (!isMobile) SizedBox(width: MySize.size24),

        // Search Bar (only on desktop)
        if (!isMobile)
          Expanded(
            flex: 1,
            child: Container(
              constraints: BoxConstraints(maxWidth: 300),
              child: CustomTextField(
                controller: controller.searchController,
                hintText: 'Search Orders',
                onChanged: controller.onSearchChanged,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: PlatformIcon(
                    iconName: 'search',
                    size: MySize.size20,
                    color: AppColors.primaryColor,
                  ),
                ),
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size20,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size10,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFormContent({bool isMobile = false}) {
    return Column(
      children: [
        // Contact Person Details Section
        _buildContactPersonDetailsCard(),

        SizedBox(height: MySize.size24),

        // Business Details Section
        _buildBusinessDetailsCard(),

        SizedBox(height: MySize.size32),

        // Add Customer Button
        _buildAddCustomerButton(),
      ],
    );
  }

  Widget _buildContactPersonDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'person',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Contact Person Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size24),

          // Form Fields Row
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                return Column(
                  children: [
                    _buildFormField(
                      label: 'Full Name',
                      controller: controller.fullNameController,
                      hintText: 'Enter Full Name',
                      isRequired: true,
                    ),
                    SizedBox(height: MySize.size16),
                    _buildMobileNumberField(),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Email ID',
                      controller: controller.emailIdController,
                      hintText: '<EMAIL>',
                      isRequired: true,
                      keyboardType: TextInputType.emailAddress,
                    ),
                  ],
                );
              }

              return Column(
                children: [
                  // First Row: Full Name and Mobile Number
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Full Name',
                          controller: controller.fullNameController,
                          hintText: 'Enter Full Name',
                          isRequired: true,
                        ),
                      ),
                      SizedBox(width: MySize.size24),
                      Expanded(child: _buildMobileNumberField()),
                    ],
                  ),

                  SizedBox(height: MySize.size20),

                  // Second Row: Email ID
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Email ID',
                          controller: controller.emailIdController,
                          hintText: '<EMAIL>',
                          isRequired: true,
                          keyboardType: TextInputType.emailAddress,
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ), // Empty space to maintain layout
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'building',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Business Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size24),

          // Form Fields
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                return Column(
                  children: [
                    _buildFormField(
                      label: 'GST Number',
                      controller: controller.gstNumberController,
                      hintText: '22AAAAA0000A1Z5',
                    ),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Business Name',
                      controller: controller.businessNameController,
                      hintText: 'Enter Business Name',
                    ),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Business Address',
                      controller: controller.businessAddressController,
                      hintText: 'Enter Business Address',
                      maxLines: 3,
                    ),
                  ],
                );
              }

              return Column(
                children: [
                  // First Row: GST Number and Business Name
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'GST Number',
                          controller: controller.gstNumberController,
                          hintText: '22AAAAA0000A1Z5',
                        ),
                      ),
                      SizedBox(width: MySize.size24),
                      Expanded(
                        child: _buildFormField(
                          label: 'Business Name',
                          controller: controller.businessNameController,
                          hintText: 'Enter Business Name',
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: MySize.size20),

                  // Second Row: Business Address
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Business Address',
                          controller: controller.businessAddressController,
                          hintText: 'Enter Business Address',
                          maxLines: 3,
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ), // Empty space to maintain layout
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    bool isRequired = false,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red, fontSize: MySize.size14),
                ),
            ],
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          maxLines: maxLines,
          keyboardType: keyboardType,
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: 'Mobile Number',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
            children: [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red, fontSize: MySize.size14),
              ),
            ],
          ),
        ),
        SizedBox(height: MySize.size8),
        Row(
          children: [
            // Country Picker
            Obx(
              () => CountryPicker(
                selectedCountry: controller.selectedCountry.value,
                onCountrySelected: controller.selectCountry,
                width: MySize.size62,
              ),
            ),
            SizedBox(width: MySize.size8),
            // Mobile Number Field
            Expanded(
              child: CustomTextField(
                controller: controller.mobileNumberController,
                hintText: 'Enter 10-Digit Mobile Number',
                keyboardType: TextInputType.phone,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size8,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size12,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAddCustomerButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: SizedBox(
        width: 200,
        child: Obx(
          () => ElevatedButton(
            onPressed:
                controller.isLoading.value ? null : controller.onAddCustomerTap,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.blackColor,
              padding: EdgeInsets.symmetric(vertical: MySize.size18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size40),
              ),
              elevation: 0,
            ),
            child:
                controller.isLoading.value
                    ? SizedBox(
                      height: MySize.size20,
                      width: MySize.size20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.blackColor,
                        ),
                      ),
                    )
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SmartIcon(
                          assetPath: 'assets/icons/tick_icon.svg',
                          height: 18,
                          width: 18,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(width: MySize.size8),
                        Text(
                          'Add Customer',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
          ),
        ),
      ),
    );
  }
}
