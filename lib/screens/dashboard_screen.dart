import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../controllers/dashboard_controller.dart';

class DashboardScreen extends GetView<DashboardController> {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: _buildMobileAppBar(),
              drawer: _buildMobileDrawer(),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              // Left Sidebar
              _buildSidebar(),

              // Main Content
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

    PreferredSizeWidget _buildMobileAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder: (context) => IconButton(
          icon: PlatformIcon(
            iconName: 'menu',
            size: MySize.size24,
            color: AppColors.blackColor,
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SmartIcon(
            assetPath: 'assets/icons/logo_icon.svg',
            height: MySize.size80,
            width: MySize.size84,
            color: AppColors.blackColor,
          ),
        ],
      ),
      centerTitle: false,
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child: Column(
                children: [
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: AppColors.blackColor.withValues(alpha: 0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),

            // Navigation Items
            Expanded(
              child: Obx(
                () => Column(
                  children: [
                    _buildMobileNavItem(
                      icon: 'home',
                      label: 'Dashboard',
                      isSelected: controller.selectedNavIndex.value == 0,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(0);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'person',
                      label: 'Customers List',
                      isSelected: controller.selectedNavIndex.value == 1,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(1);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'shopping_cart',
                      label: 'Orders List',
                      isSelected: controller.selectedNavIndex.value == 2,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(2);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),

          SizedBox(height: MySize.size32),

          // Dashboard Grid
          Expanded(child: _buildDashboardGrid()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Welcome Text
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                      'Welcome Back Milan',
                      style: TextStyle(
                        fontSize: MySize.size24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: MySize.size8),
                  SmartIcon(
                    assetPath: 'assets/svg/hello_icon.svg',
                    height: 24,
                    width: 24,
                  ),
                ],
              ),
              SizedBox(height: MySize.size4),
              Text(
                "Let's get your orders moving",
                style: TextStyle(
                  fontSize: MySize.size16,
                  color: AppColors.textSecondary,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        SizedBox(width: MySize.size16),

        // Search Bar
        Expanded(
          flex: 1,
          child: Container(
            constraints: BoxConstraints(maxWidth: 300, minWidth: 200),
            child: CustomTextField(
              controller: controller.searchController,
              hintText: 'Search Orders',
              onChanged: controller.onSearchChanged,
              prefixIcon: Padding(
                padding: EdgeInsets.all(MySize.size12),
                child: PlatformIcon(
                  iconName: 'search',
                  size: MySize.size20,
                  color: AppColors.primaryColor,
                ),
              ),
              fillColor: Colors.white,
              borderColor: AppColors.borderColor,
              borderRadius: MySize.size20,
              contentPadding: EdgeInsets.symmetric(
                horizontal: MySize.size16,
                vertical: MySize.size10,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDashboardGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive cross axis count based on available width
        int crossAxisCount = 4; // Default for large screens

        if (constraints.maxWidth < 600) {
          crossAxisCount = 1; // Mobile: 1 column
        } else if (constraints.maxWidth < 900) {
          crossAxisCount = 2; // Tablet: 2 columns
        } else if (constraints.maxWidth < 1200) {
          crossAxisCount = 3; // Small desktop: 3 columns
        } else {
          crossAxisCount = 4; // Large desktop: 4 columns
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: MySize.size12,
            mainAxisSpacing: MySize.size12,
            childAspectRatio: 2,
          ),
          itemCount: _getDashboardItems().length,
          itemBuilder: (context, index) {
            final item = _getDashboardItems()[index];
            return _buildDashboardCard(
              title: item['title'],
              count: item['count'],
              iconPath: item['iconPath'],
              iconColor: item['iconColor'],
            );
          },
        );
      },
    );
  }

  List<Map<String, dynamic>> _getDashboardItems() {
    return [
      {
        'title': 'Total Orders',
        'count': '45',
        'iconPath': 'assets/icons/onboarding_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'On-boarding',
        'count': '5',
        'iconPath': 'assets/icons/onboarding_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Designing',
        'count': '5',
        'iconPath': 'assets/icons/designing_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Sampling',
        'count': '6',
        'iconPath': 'assets/icons/sampling_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Design Plate Approved',
        'count': '5',
        'iconPath': 'assets/icons/design_plate_approval_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Cylinder Development',
        'count': '5',
        'iconPath': 'assets/icons/cylinder_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Polyester Sample Approved',
        'count': '5',
        'iconPath': 'assets/icons/polyster_sample_approved_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Polyester Printing',
        'count': '1',
        'iconPath': 'assets/icons/polyster_printing_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Lamination',
        'count': '1',
        'iconPath': 'assets/icons/lamination_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Metallised Pasting',
        'count': '1',
        'iconPath': 'assets/icons/pasting_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Heating',
        'count': '1',
        'iconPath': 'assets/icons/heating_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Curing',
        'count': '1',
        'iconPath': 'assets/icons/curing_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Zipper Addition',
        'count': '1',
        'iconPath': 'assets/icons/zipper_addition_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Slitting',
        'count': '1',
        'iconPath': 'assets/icons/slitting_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Pouching',
        'count': '1',
        'iconPath': 'assets/icons/pouching_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Sorting',
        'count': '1',
        'iconPath': 'assets/icons/sorting_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Packing',
        'count': '1',
        'iconPath': 'assets/icons/packing_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Ready to Dispatch',
        'count': '1',
        'iconPath': 'assets/icons/ready_to_dispatch_icon.png',
        'iconColor': AppColors.blackColor,
      },
      {
        'title': 'Dispatched',
        'count': '1',
        'iconPath': 'assets/icons/dispatched_icon.png',
        'iconColor': AppColors.blackColor,
      },
    ];
  }

  Widget _buildDashboardCard({
    required String title,
    required String count,
    required String iconPath,
    required Color iconColor,
  }) {
    return GestureDetector(
      onTap: () => controller.onCardTap(title, int.tryParse(count) ?? 0),
      child: Container(
        padding: EdgeInsets.all(MySize.size12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: const Color(0x0C0C0C0D),
              blurRadius: 4,
              spreadRadius: -4,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: const Color(0x1A0C0C0D),
              blurRadius: 32,
              spreadRadius: -4,
              offset: const Offset(0, 16),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Icon and Title Row
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  width: MySize.size44,
                  height: MySize.size44,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size10),
                  ),
                  child: Center(
                    child: SmartIcon(
                      assetPath: iconPath,
                      width: MySize.size24,
                      height: MySize.size24,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: MySize.size20),

            // Count
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  count,
                  style: TextStyle(
                    fontSize: MySize.size40,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(width: MySize.size8),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
